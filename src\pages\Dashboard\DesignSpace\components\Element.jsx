import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { isEmpty } from "lodash";

export default function Element({ el, scaleFactor = 1, userData = {} }) {

    switch (el.type) {
        case "img":
            // If it's an avatar and we have user data, use the user's image
            if (!isEmpty(userData) && el.value === "avatar") {
                el = { ...el, value: userData?.image || "https://www.gravatar.com/avatar/?d=mp" };
            }
            return <Image el={el} />

        case "qr":
            return <QrImage el={el} />

        case "icon":
            return <Icon el={el} />

        case "text":
            el = !isEmpty(userData) ? {...el, value: userData?.[el.value]} : el
            return <Text el={el} scaleFactor={scaleFactor} />

        case "label":
            return <Label el={el} scaleFactor={scaleFactor} />

        case "shape":
            return <Shape el={el} />

        case "line":
            return <Line el={el} />

        case "frame":
            return <Frame el={el} />

        case "sticker":
            return <Sticker el={el} />

        case "gradient":
            return <Gradient el={el} />

        case "svg":
            return <SVGElement el={el} />

        default:
            break;
    }
}

const Image = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            alt="holder"
            style={{
                borderRadius: el.borderRadius,
                width: "100%",
                height: "100%",
            }}
        />)
}

const QrImage = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            alt="Qr Code"
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}


const Label = ({ el, scaleFactor }) => {
    const { updateElement } = useDesignSpace();

    return (
        <input
            type="text"
            value={el.value || "Enter your text here"}
            placeholder="Enter your text here"
            onChange={(e) => updateElement(el.id, { value: e.target.value })}
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily,
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform,
                textAlign: el.textAlign,
                fontSize: el.fontSize * scaleFactor,
                overflow: "visible",
                padding: "2px",
                color: el.color,
                height: "100%",
                width: "100%",
            }} />
    )
}

const Text = ({ el, scaleFactor }) => {
    return (
        <div
            className="user-data"
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily,
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform,
                textAlign: el.textAlign,
                fontSize: el.fontSize,
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflow: "visible",
                padding: "2px",
                color: el.color,
                height: "100%",
                width: "100%",
            }}
        >
            {el.value || "Your text content here"}
        </div>
    )
}

const Icon = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}

// Shape component for rendering different shapes
const Shape = ({ el }) => {
    const shapeStyles = {
        width: "100%",
        height: "100%",
        backgroundColor: el.backgroundColor || "#4338ca",
        userSelect: "none",
    };

    switch (el.shapeType) {
        case "rectangle":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...shapeStyles, borderRadius: "50%" }}></div>;
        case "triangle":
            return (
                <div
                    style={{
                        width: "0",
                        height: "0",
                        borderLeft: `${el.width / 2}px solid transparent`,
                        borderRight: `${el.width / 2}px solid transparent`,
                        borderBottom: `${el.height}px solid ${el.backgroundColor || "#4338ca"}`,
                    }}
                ></div>
            );
        case "star":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ★
                </div>
            );
        case "heart":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ❤
                </div>
            );
        case "pentagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="50,0 100,38 81,100 19,100 0,38" />
                </svg>
            );
        case "hexagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="25,0 75,0 100,50 75,100 25,100 0,50" />
                </svg>
            );
        case "diamond":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="50,0 100,50 50,100 0,50" />
                </svg>
            );
        case "rounded-square":
            return <div style={{ ...shapeStyles, borderRadius: "15px" }}></div>;
        case "oval":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <ellipse cx="50" cy="50" rx="50" ry="35" />
                </svg>
            );
        case "trapezoid":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="20,0 80,0 100,100 0,100" />
                </svg>
            );
        case "parallelogram":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="25,0 100,0 75,100 0,100" />
                </svg>
            );
        case "octagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || "#4338ca" }}>
                    <polygon points="30,0 70,0 100,30 100,70 70,100 30,100 0,70 0,30" />
                </svg>
            );
        default:
            return <div style={shapeStyles}></div>;
    }
};

// Line component for rendering different line types
const Line = ({ el }) => {
    const lineStyles = {
        width: "100%",
        height: "100%",
        position: "relative",
        userSelect: "none",
    };

    switch (el.lineType) {
        case "straight":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2 }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
        case "arrow-right":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <line x1="0" y1="10" x2="90" y2="10" />
                    <polyline points="80,0 100,10 80,20" />
                </svg>
            );
        case "arrow-left":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <line x1="10" y1="10" x2="100" y2="10" />
                    <polyline points="20,0 0,10 20,20" />
                </svg>
            );
        case "arrow-up":
            return (
                <svg width="100%" height="100%" viewBox="0 0 20 100" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <line x1="10" y1="10" x2="10" y2="100" />
                    <polyline points="0,20 10,0 20,20" />
                </svg>
            );
        case "arrow-down":
            return (
                <svg width="100%" height="100%" viewBox="0 0 20 100" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <line x1="10" y1="0" x2="10" y2="90" />
                    <polyline points="0,80 10,100 20,80" />
                </svg>
            );
        case "arrow-big-right":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 30" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 3, fill: "none" }}>
                    <line x1="0" y1="15" x2="70" y2="15" />
                    <polyline points="60,0 100,15 60,30" />
                </svg>
            );
        case "arrow-big-left":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 30" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 3, fill: "none" }}>
                    <line x1="30" y1="15" x2="100" y2="15" />
                    <polyline points="40,0 0,15 40,30" />
                </svg>
            );
        case "arrow-circle-right":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <line x1="0" y1="10" x2="80" y2="10" />
                    <circle cx="90" cy="10" r="10" />
                    <polyline points="85,5 95,10 85,15" />
                </svg>
            );
        case "arrow-wave":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 30" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <path d="M0,15 Q25,0 50,15 T100,15" />
                    <polyline points="85,5 100,15 85,25" />
                </svg>
            );
        case "curved":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 50" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2, fill: "none" }}>
                    <path d="M10,40 C30,10 70,10 90,40" />
                </svg>
            );
        default:
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: el.strokeColor || "#4338ca", strokeWidth: el.strokeWidth || 2 }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
    }
};

// Frame component for rendering different frame types
const Frame = ({ el }) => {
    const frameStyles = {
        width: "100%",
        height: "100%",
        border: `${el.borderWidth || 2}px solid ${el.borderColor || "#4338ca"}`,
        backgroundColor: "transparent",
        userSelect: "none",
    };

    switch (el.frameType) {
        case "rectangle":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...frameStyles, borderRadius: "50%" }}></div>;
        case "heart":
            return (
                <div
                    style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: el.borderColor || "#4338ca",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                    }}
                >
                    ♡
                </div>
            );
        default:
            return <div style={frameStyles}></div>;
    }
};

// Sticker component for rendering different sticker types
const Sticker = ({ el }) => {
    // Emoji mapping for stickers
    const emojiMap = {
        // Emoji stickers
        "smile": "😊",
        "laugh": "😂",
        "love": "😍",
        "cool": "😎",
        "wink": "😉",
        "think": "🤔",
        "wow": "😮",
        "sad": "😢",
        "angry": "😡",

        // Symbol stickers
        "star": "⭐",
        "heart": "❤️",
        "fire": "🔥",
        "check": "✅",
        "cross": "❌",
        "warning": "⚠️",
        "lightning": "⚡",
        "music": "🎵",
        "trophy": "🏆",

        // Object stickers
        "gift": "🎁",
        "balloon": "🎈",
        "camera": "📷",
        "phone": "📱",
        "computer": "💻",
        "bulb": "💡",
        "money": "💰",
        "rocket": "🚀",
        "clock": "🕒",

        // Default for backward compatibility
        "arrow": "➡️",
        "shapes": "🔶"
    };

    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: Math.min(el.width, el.height) * 0.6,
                userSelect: "none",
            }}
        >
            {emojiMap[el.stickerType] || "🔹"}
        </div>
    );
};

// Gradient component for rendering gradient backgrounds
const Gradient = ({ el }) => {
    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                background: el.style?.background || "linear-gradient(45deg, #1e3c72, #2a5298)",
                borderRadius: "8px",
                userSelect: "none",
            }}
        ></div>
    );
};

// SVG component for rendering complex SVG elements
const SVGElement = ({ el }) => {
    // Check if the SVG content is available
    if (!el.value) {
        return (
            <div
                style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#f0f0f0",
                    color: "#666",
                    border: "1px dashed #999",
                    borderRadius: "4px",
                    padding: "8px",
                    fontSize: "12px",
                    textAlign: "center"
                }}
            >
                SVG content not available
            </div>
        );
    }

    // For security, we'll use dangerouslySetInnerHTML but only for SVG content
    // This is generally not recommended for user-generated content without proper sanitization
    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
            }}
            dangerouslySetInnerHTML={{ __html: el.value }}
        />
    );
};